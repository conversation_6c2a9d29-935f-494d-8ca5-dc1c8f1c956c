#!/usr/bin/env node
// Performance test for anthropic-proxy

async function performanceTest() {
  console.log('\n=== 性能测试：并发请求 ===')
  
  const requests = []
  const startTime = Date.now()
  
  // 创建5个并发请求
  for (let i = 0; i < 5; i++) {
    const request = fetch('http://localhost:3000/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-sonnet',
        max_tokens: 50,
        messages: [
          {
            role: 'user',
            content: `这是第${i + 1}个并发请求，请简短回复。`
          }
        ]
      })
    }).then(async (response) => {
      const data = await response.json()
      return {
        requestId: i + 1,
        status: response.status,
        responseTime: Date.now() - startTime,
        contentLength: data.content?.[0]?.text?.length || 0
      }
    }).catch(error => ({
      requestId: i + 1,
      error: error.message
    }))
    
    requests.push(request)
  }
  
  try {
    const results = await Promise.all(requests)
    const endTime = Date.now()
    const totalTime = endTime - startTime
    
    console.log('并发测试结果:')
    results.forEach(result => {
      if (result.error) {
        console.log(`请求 ${result.requestId}: 错误 - ${result.error}`)
      } else {
        console.log(`请求 ${result.requestId}: 状态 ${result.status}, 响应时间 ${result.responseTime}ms, 内容长度 ${result.contentLength}字符`)
      }
    })
    
    const successCount = results.filter(r => !r.error && r.status === 200).length
    console.log(`\n总结:`)
    console.log(`- 总时间: ${totalTime}ms`)
    console.log(`- 成功请求: ${successCount}/${results.length}`)
    console.log(`- 成功率: ${(successCount / results.length * 100).toFixed(1)}%`)
    
  } catch (error) {
    console.error('性能测试失败:', error)
  }
}

performanceTest()
