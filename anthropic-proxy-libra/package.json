{"name": "anthropic-proxy", "description": "A proxy server that transforms Anthropic API requests to OpenAI format and sends it to openrouter.ai.", "version": "1.3.0", "type": "module", "bin": {"anthropic-proxy": "index.js"}, "scripts": {"start": "node index.js"}, "keywords": ["anthropic", "openai", "api", "proxy", "claude", "streaming", "sse"], "repository": {"type": "git", "url": "https://github.com/maxnowack/anthropic-proxy"}, "bugs": {"url": "https://github.com/maxnowack/anthropic-proxy/issues"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"fastify": "^5.2.1"}}