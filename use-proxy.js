#!/usr/bin/env node
// 使用代理服务器进行对话

async function useProxy() {
  console.log('🤖 正在通过 anthropic-proxy 调用 Claude...\n')
  
  try {
    const response = await fetch('http://localhost:3000/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-sonnet',
        max_tokens: 200,
        messages: [
          {
            role: 'user',
            content: '你好！我现在正在通过一个代理服务器与你对话。这个代理服务器将 Anthropic API 格式转换为 OpenAI 格式，然后通过 OpenRouter 来访问你。请简单介绍一下你自己，并说说你对这种代理方式的看法。'
          }
        ]
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 通过代理成功连接到 Claude！')
      console.log('🎯 模型:', data.model)
      console.log('📝 Claude 的回复:\n')
      console.log(data.content[0].text)
      console.log(`\n📊 Token 使用: 输入 ${data.usage.input_tokens}，输出 ${data.usage.output_tokens}`)
      console.log('🔄 代理转换: Anthropic API → OpenAI API → OpenRouter → Claude')
    } else {
      console.log('❌ 代理请求失败:', response.status)
      console.log(await response.text())
    }
  } catch (error) {
    console.log('❌ 代理连接错误:', error.message)
  }
}

useProxy()
