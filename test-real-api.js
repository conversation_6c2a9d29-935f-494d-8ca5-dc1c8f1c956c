#!/usr/bin/env node
// Test script for real OpenRouter API through anthropic-proxy

async function testRealAPI() {
  console.log('\n=== 测试真实 OpenRouter API ===')
  
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'anthropic/claude-3-sonnet',
      max_tokens: 150,
      messages: [
        {
          role: 'user',
          content: '你好！请用中文回复一个简短的问候，并告诉我你是什么模型。'
        }
      ]
    })
  })
  
  console.log('状态码:', response.status)
  console.log('响应头:', Object.fromEntries(response.headers.entries()))
  
  if (response.ok) {
    const data = await response.json()
    console.log('响应数据:', JSON.stringify(data, null, 2))
  } else {
    console.log('错误:', await response.text())
  }
}

async function testStreamingReal() {
  console.log('\n=== 测试真实流式响应 ===')
  
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'anthropic/claude-3-sonnet',
      max_tokens: 100,
      stream: true,
      messages: [
        {
          role: 'user',
          content: '请用流式方式回复：数到5，每个数字用一句话解释。'
        }
      ]
    })
  })
  
  console.log('状态码:', response.status)
  
  if (response.ok && response.body) {
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    
    console.log('流式响应内容:')
    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        
        const chunk = decoder.decode(value)
        // 只显示文本内容，不显示完整的SSE格式
        const lines = chunk.split('\n')
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6))
              if (data.type === 'content_block_delta' && data.delta?.text) {
                process.stdout.write(data.delta.text)
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
      console.log('\n流式响应完成')
    } catch (error) {
      console.error('流式错误:', error)
    }
  } else {
    console.log('错误:', await response.text())
  }
}

async function testToolCallsReal() {
  console.log('\n=== 测试真实工具调用 ===')
  
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'anthropic/claude-3-sonnet',
      max_tokens: 200,
      messages: [
        {
          role: 'user',
          content: '北京今天的天气怎么样？请使用工具查询。'
        }
      ],
      tools: [
        {
          name: 'get_weather',
          description: '获取指定城市的当前天气信息',
          input_schema: {
            type: 'object',
            properties: {
              city: {
                type: 'string',
                description: '城市名称，例如：北京、上海、广州'
              },
              unit: {
                type: 'string',
                enum: ['celsius', 'fahrenheit'],
                description: '温度单位'
              }
            },
            required: ['city']
          }
        }
      ]
    })
  })
  
  console.log('状态码:', response.status)
  
  if (response.ok) {
    const data = await response.json()
    console.log('工具调用响应:', JSON.stringify(data, null, 2))
  } else {
    console.log('错误:', await response.text())
  }
}

async function runRealTests() {
  try {
    await testRealAPI()
    await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
    
    await testStreamingReal()
    await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
    
    await testToolCallsReal()
  } catch (error) {
    console.error('测试错误:', error)
  }
}

runRealTests()
