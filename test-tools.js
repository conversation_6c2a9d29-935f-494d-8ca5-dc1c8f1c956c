#!/usr/bin/env node
// Test tool calls functionality

async function testToolCalls() {
  console.log('\n=== Testing Tool Calls ===')
  
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 100,
      messages: [
        {
          role: 'user',
          content: 'What is the weather like today?'
        }
      ],
      tools: [
        {
          name: 'get_weather',
          description: 'Get the current weather for a location',
          input_schema: {
            type: 'object',
            properties: {
              location: {
                type: 'string',
                description: 'The city and state, e.g. San Francisco, CA'
              }
            },
            required: ['location']
          }
        }
      ]
    })
  })
  
  console.log('Status:', response.status)
  console.log('Headers:', Object.fromEntries(response.headers.entries()))
  
  if (response.ok) {
    const data = await response.json()
    console.log('Response:', JSON.stringify(data, null, 2))
  } else {
    console.log('Error:', await response.text())
  }
}

testToolCalls()
