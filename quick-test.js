#!/usr/bin/env node
// 快速测试代理服务器

async function quickTest() {
  console.log('🚀 正在测试 anthropic-proxy...\n')
  
  try {
    const response = await fetch('http://localhost:3000/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-sonnet',
        max_tokens: 80,
        messages: [
          {
            role: 'user',
            content: '你好！请简单介绍一下你自己。'
          }
        ]
      })
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 代理服务器工作正常！')
      console.log('📝 Claude 的回复:')
      console.log(data.content[0].text)
      console.log(`\n📊 使用情况: 输入${data.usage.input_tokens}个token，输出${data.usage.output_tokens}个token`)
    } else {
      console.log('❌ 请求失败:', response.status)
      console.log(await response.text())
    }
  } catch (error) {
    console.log('❌ 连接错误:', error.message)
  }
}

quickTest()
