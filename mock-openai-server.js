#!/usr/bin/env node
// Simple mock OpenAI API server for testing the proxy
import http from 'http'
import url from 'url'

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true)

  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    res.writeHead(200)
    res.end()
    return
  }

  if (req.method === 'POST' && parsedUrl.pathname === '/v1/chat/completions') {
    let body = ''
    req.on('data', chunk => {
      body += chunk.toString()
    })

    req.on('end', () => {
      try {
        const requestData = JSON.parse(body)
        console.log('Received request:', JSON.stringify(requestData, null, 2))

        const { model, messages, max_tokens, temperature, stream } = requestData

        // Mock response in OpenAI format
        const mockResponse = {
          id: 'chatcmpl-test123',
          object: 'chat.completion',
          created: Math.floor(Date.now() / 1000),
          model: model || 'gpt-3.5-turbo',
          choices: [{
            index: 0,
            message: {
              role: 'assistant',
              content: 'Hello! This is a mock response from the test OpenAI server. Your message was received and processed successfully.'
            },
            finish_reason: 'stop'
          }],
          usage: {
            prompt_tokens: 20,
            completion_tokens: 25,
            total_tokens: 45
          }
        }

        res.setHeader('Content-Type', 'application/json')
        res.writeHead(200)
        res.end(JSON.stringify(mockResponse))

      } catch (error) {
        console.error('Error parsing request:', error)
        res.writeHead(400)
        res.end(JSON.stringify({ error: 'Invalid JSON' }))
      }
    })
  } else {
    res.writeHead(404)
    res.end(JSON.stringify({ error: 'Not found' }))
  }
})

server.listen(3001, '0.0.0.0', () => {
  console.log('Mock OpenAI server listening on http://localhost:3001')
})
