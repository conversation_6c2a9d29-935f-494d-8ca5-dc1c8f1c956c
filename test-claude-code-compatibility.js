#!/usr/bin/env node
// 测试 Claude Code 兼容性的完整测试程序

async function testClaudeCodeModel() {
  console.log('🧪 测试 Claude Code 模型兼容性...\n')
  
  try {
    // 测试 Claude Code 使用的模型 ID
    const response = await fetch('http://localhost:3000/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'claude-sonnet-4-20250514', // Claude Code 使用的模型 ID
        max_tokens: 100,
        messages: [
          {
            role: 'user',
            content: '你是什么模型？请简短回答。'
          }
        ]
      })
    })
    
    console.log('📊 响应状态:', response.status)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ 成功！Claude Code 模型映射工作正常')
      console.log('🤖 Claude 回复:', data.content[0].text)
      console.log('📈 实际使用的模型:', data.model)
      console.log('🔢 Token 使用:', `输入 ${data.usage.input_tokens}, 输出 ${data.usage.output_tokens}`)
      return true
    } else {
      const errorText = await response.text()
      console.log('❌ 失败:', errorText)
      return false
    }
  } catch (error) {
    console.log('❌ 连接错误:', error.message)
    return false
  }
}

async function testOtherModelMappings() {
  console.log('\n🔄 测试其他模型映射...\n')
  
  const testModels = [
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
    'claude-3-5-sonnet-20241022'
  ]
  
  for (const modelId of testModels) {
    try {
      console.log(`测试模型: ${modelId}`)
      
      const response = await fetch('http://localhost:3000/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: modelId,
          max_tokens: 50,
          messages: [
            {
              role: 'user',
              content: '简单说声你好'
            }
          ]
        })
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${modelId} → ${data.model} (成功)`)
      } else {
        console.log(`❌ ${modelId} 失败: ${response.status}`)
      }
    } catch (error) {
      console.log(`❌ ${modelId} 错误: ${error.message}`)
    }
    
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
}

async function testStreamingWithMapping() {
  console.log('\n🌊 测试流式响应与模型映射...\n')
  
  try {
    const response = await fetch('http://localhost:3000/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'claude-sonnet-4-20250514', // Claude Code 模型
        max_tokens: 60,
        stream: true,
        messages: [
          {
            role: 'user',
            content: '请用流式方式数到3，每个数字说一句话。'
          }
        ]
      })
    })
    
    if (response.ok && response.body) {
      console.log('✅ 流式响应开始:')
      
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        
        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6))
              if (data.type === 'content_block_delta' && data.delta?.text) {
                process.stdout.write(data.delta.text)
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
      
      console.log('\n✅ 流式响应完成！')
      return true
    } else {
      console.log('❌ 流式请求失败:', response.status)
      return false
    }
  } catch (error) {
    console.log('❌ 流式测试错误:', error.message)
    return false
  }
}

async function runAllTests() {
  console.log('🚀 开始 anthropic-proxy Claude Code 兼容性测试\n')
  console.log('=' .repeat(60))
  
  const results = []
  
  // 测试1: Claude Code 模型兼容性
  results.push(await testClaudeCodeModel())
  
  // 测试2: 其他模型映射
  await testOtherModelMappings()
  
  // 测试3: 流式响应
  results.push(await testStreamingWithMapping())
  
  console.log('\n' + '=' .repeat(60))
  console.log('📋 测试总结:')
  
  const successCount = results.filter(r => r).length
  const totalTests = results.length
  
  if (successCount === totalTests) {
    console.log('🎉 所有核心测试通过！anthropic-proxy 已修复 Claude Code 兼容性问题')
    console.log('✅ Claude Code 现在可以正常使用代理服务器了')
  } else {
    console.log(`⚠️  ${successCount}/${totalTests} 个核心测试通过`)
  }
  
  console.log('\n💡 使用方法:')
  console.log('   ANTHROPIC_BASE_URL=http://0.0.0.0:3000 claude')
  console.log('\n🔧 代理服务器启动命令:')
  console.log('   OPENROUTER_API_KEY=your-key node index.js')
}

runAllTests()
