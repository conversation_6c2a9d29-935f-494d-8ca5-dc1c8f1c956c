#!/usr/bin/env node
// 流式响应测试

async function streamTest() {
  console.log('🌊 正在测试流式响应...\n')
  
  try {
    const response = await fetch('http://localhost:3000/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-sonnet',
        max_tokens: 60,
        stream: true,
        messages: [
          {
            role: 'user',
            content: '请用流式方式说一首简短的中文诗。'
          }
        ]
      })
    })
    
    if (response.ok && response.body) {
      console.log('✅ 开始接收流式响应:')
      console.log('📝 Claude 正在创作...\n')
      
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        
        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6))
              if (data.type === 'content_block_delta' && data.delta?.text) {
                process.stdout.write(data.delta.text)
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
      
      console.log('\n\n🎉 流式响应完成！')
    } else {
      console.log('❌ 流式请求失败:', response.status)
    }
  } catch (error) {
    console.log('❌ 流式测试错误:', error.message)
  }
}

streamTest()
