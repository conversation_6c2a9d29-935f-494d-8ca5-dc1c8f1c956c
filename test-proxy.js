#!/usr/bin/env node
// Test script for the anthropic-proxy

async function testNonStreaming() {
  console.log('\n=== Testing Non-Streaming Request ===')
  
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 50,
      messages: [
        {
          role: 'user',
          content: 'Hello, please respond with a greeting.'
        }
      ]
    })
  })
  
  console.log('Status:', response.status)
  console.log('Headers:', Object.fromEntries(response.headers.entries()))
  
  if (response.ok) {
    const data = await response.json()
    console.log('Response:', JSON.stringify(data, null, 2))
  } else {
    console.log('Error:', await response.text())
  }
}

async function testStreaming() {
  console.log('\n=== Testing Streaming Request ===')
  
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 50,
      stream: true,
      messages: [
        {
          role: 'user',
          content: 'Hello, please respond with a streaming greeting.'
        }
      ]
    })
  })
  
  console.log('Status:', response.status)
  console.log('Headers:', Object.fromEntries(response.headers.entries()))
  
  if (response.ok && response.body) {
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    
    console.log('Streaming response:')
    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        
        const chunk = decoder.decode(value)
        console.log('Chunk:', chunk)
      }
    } catch (error) {
      console.error('Streaming error:', error)
    }
  } else {
    console.log('Error:', await response.text())
  }
}

async function testErrorHandling() {
  console.log('\n=== Testing Error Handling ===')
  
  // Test with invalid JSON
  const response = await fetch('http://localhost:3000/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: 'invalid json'
  })
  
  console.log('Status:', response.status)
  if (!response.ok) {
    console.log('Error response:', await response.text())
  }
}

async function runTests() {
  try {
    await testNonStreaming()
    await testStreaming()
    await testErrorHandling()
  } catch (error) {
    console.error('Test error:', error)
  }
}

runTests()
